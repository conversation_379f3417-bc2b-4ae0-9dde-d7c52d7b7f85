package config

import (
	"errors"
	"fmt"
	"time"

	"github.com/rs/zerolog"
)

var errNoPathForDatabase = errors.New("no path configured for database")

type ManagementConfig struct {
	Base `mapstructure:",squash"`

	Auth struct {
		JWTSecret     string        `mapstructure:"jwt_secret"`
		JWTExpiration time.Duration `mapstructure:"jwt_expiration"`
	} `mapstructure:"auth"`

	Database struct {
		Path string `mapstructure:"path"`

		// GC Configuration
		GCInterval     time.Duration `mapstructure:"gc_interval"`
		GCDiscardRatio float64       `mapstructure:"gc_discard_ratio"`
	} `mapstructure:"database"`

	Relay struct {
		ConfigurationServer struct {
			Address string `mapstructure:"address"`
		} `mapstructure:"configuration_server"`
		StreamServer struct {
			Address string `mapstructure:"address"`
		} `mapstructure:"stream_server"`
	} `mapstructure:"relay"`

	Grpc struct {
		RequestTimeout time.Duration `mapstructure:"request_timeout"`
	} `mapstructure:"grpc"`
}

func NewManagementConfig(files ...string) (c ManagementConfig, err error) {
	c = ManagementConfig{
		Base: NewCommonConfig(),
	}

	c.Loglevel = zerolog.InfoLevel.String()
	c.ListenAddressHTTP = ":8003"
	c.ListenAddressPrometheus = ":8004"

	c.Auth.JWTExpiration = 24 * time.Hour //nolint: mnd

	c.Database.Path = "/var/lib/badgerdb/probe/data"
	c.Database.GCInterval = 24 * time.Hour //nolint: mnd
	c.Database.GCDiscardRatio = 0.7

	defaultRelayGRPCAddress := ":8010"
	c.Relay.ConfigurationServer.Address = defaultRelayGRPCAddress
	c.Relay.StreamServer.Address = defaultRelayGRPCAddress

	c.Grpc.RequestTimeout = 5 * time.Second //nolint: mnd

	m, err := NewManager(&c, files...)
	if err != nil {
		err = fmt.Errorf("cannot create new config manager: %w", err)
		return
	}

	err = m.Load()
	if err != nil {
		err = fmt.Errorf("cannot load configuration: %w", err)
		return
	}

	if c.Database.Path == "" {
		err = errNoPathForDatabase
		return
	}

	return
}
