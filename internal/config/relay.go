package config

import (
	"fmt"
	"time"

	"github.com/rs/zerolog"
)

type RelayConfig struct {
	Base `mapstructure:",squash"`

	ListenAddressGRPC string `mapstructure:"listen_address_grpc"`
	Database          struct {
		Path string `mapstructure:"path"`

		// GC Configuration
		GCInterval     time.Duration `mapstructure:"gc_interval"`
		GCDiscardRatio float64       `mapstructure:"gc_discard_ratio"`
	} `mapstructure:"database"`
}

func NewRelayConfig(files ...string) (c RelayConfig, err error) {
	c = RelayConfig{
		Base: NewCommonConfig(),
	}

	c.Loglevel = zerolog.InfoLevel.String()
	c.ListenAddressHTTP = ":8008"
	c.ListenAddressPrometheus = ":8009"
	c.ListenAddressGRPC = ":8010"
	c.Database.Path = "data/relay/badgerdb"
	c.Database.GCInterval = 24 * time.Hour //nolint: mnd
	c.Database.GCDiscardRatio = 0.7

	m, err := NewManager(&c, files...)
	if err != nil {
		err = fmt.Errorf("cannot create new config manager: %w", err)
		return
	}

	err = m.Load()
	if err != nil {
		err = fmt.Errorf("cannot load configuration: %w", err)
		return
	}

	return
}
