package badgerdb_test

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestNewGarbageCollector(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NotNil(t, gc)
}

func TestGarbageCollector_StartStop(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test start
	err = gc.Start()
	require.NoError(t, err)

	// Test stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_RunGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test manual GC run
	err = gc.RunGC()
	require.NoError(t, err)
}

func TestGarbageCollector_DoubleStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Start first time
	err = gc.Start()
	require.NoError(t, err)

	// Try to start again - should fail
	err = gc.Start()
	require.Error(t, err)
	require.Contains(t, err.Error(), "already running")

	// Stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_StopWithoutStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Stop without starting - should not error
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_ActualGarbageCollection(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_gc.db")

	// Create test configuration with aggressive GC settings
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     50 * time.Millisecond, // Very short interval for testing
		GCDiscardRatio: 0.1,                   // Low threshold for testing
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database with settings that encourage value log files and force large values into vlog
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 2 << 20 // 2MB value log files
	dbOptions.ValueThreshold = 512       // Force values > 512 bytes into value log
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("Phase 1: Creating large data to fill multiple value log files")

	// Create large amounts of data to force multiple value log files
	err = createLargeTestDataForGC(db, 2000, 2048) // 2000 entries with 2KB each = ~4MB total
	require.NoError(t, err)

	// Force sync to ensure data is written to value log files
	require.NoError(t, db.Sync())

	t.Log("Phase 2: Updating data to create stale entries")

	// Update the data to create stale entries in value log
	err = updateLargeTestDataForGC(db, 2000, 2048)
	require.NoError(t, err)

	require.NoError(t, db.Sync())

	t.Log("Phase 3: Deleting data to create garbage")

	// Delete significant portion of data to create garbage
	err = deleteLargeTestDataForGC(db, 1500) // Delete 75% of data
	require.NoError(t, err)

	require.NoError(t, db.Sync())

	t.Log("Phase 4: Running garbage collection")

	// Create garbage collector and start it
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let the garbage collector run for a while
	time.Sleep(200 * time.Millisecond)

	// Manually trigger GC multiple times to ensure it runs
	gcRuns := 0
	for i := 0; i < 10; i++ {
		err = gc.RunGC()
		if err == nil {
			gcRuns++
			t.Logf("GC run %d successful", gcRuns)
		} else if errors.Is(err, badger.ErrNoRewrite) {
			t.Logf("GC run %d: No rewrite needed", i+1)
		} else {
			require.NoError(t, err, "Unexpected GC error")
		}
		time.Sleep(10 * time.Millisecond)
	}

	t.Logf("Completed %d successful GC runs", gcRuns)

	// Verify database is still functional
	err = verifyDataIntegrity(db)
	require.NoError(t, err)

	t.Log("✅ Garbage collection test completed successfully")
}

// Helper function to create large test data.
func createLargeTestData(db *badger.DB, count int, valueSize int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))

			// Create large value
			value := map[string]interface{}{
				"id":        i,
				"data":      generateLargeString(valueSize),
				"timestamp": time.Now().Unix(),
			}

			valueBytes, err := json.Marshal(value)
			if err != nil {
				return err
			}

			if err := txn.Set(key, valueBytes); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to update test data (creates stale entries).
func updateTestData(db *badger.DB, count int, valueSize int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))

			// Create updated large value
			value := map[string]interface{}{
				"id":        i,
				"data":      generateLargeString(valueSize) + "_updated",
				"timestamp": time.Now().Unix(),
			}

			valueBytes, err := json.Marshal(value)
			if err != nil {
				return err
			}

			if err := txn.Set(key, valueBytes); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to delete test data.
func deleteTestData(db *badger.DB, count int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))
			if err := txn.Delete(key); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to generate large string.
func generateLargeString(size int) string {
	data := make([]byte, size)
	for i := range data {
		data[i] = byte('A' + (i % 26))
	}
	return string(data)
}

// Helper functions for improved GC testing

// createLargeTestDataForGC creates large test data specifically designed to trigger value log GC.
func createLargeTestDataForGC(db *badger.DB, count int, valueSize int) error {
	batchSize := 500

	for start := 0; start < count; start += batchSize {
		end := start + batchSize
		if end > count {
			end = count
		}

		// Create new batch for each chunk
		batch := db.NewWriteBatch()

		for i := start; i < end; i++ {
			key := []byte(fmt.Sprintf("gc_test_key_%06d", i))

			// Create large value that will definitely go to value log
			value := make([]byte, valueSize)
			for j := range value {
				value[j] = byte('A' + ((i + j) % 26))
			}

			// Add some structured data to make it more realistic
			prefix := fmt.Sprintf("DATA_%06d:", i)
			copy(value[:len(prefix)], prefix)

			err := batch.Set(key, value)
			if err != nil {
				batch.Cancel()
				return err
			}
		}

		// Flush this batch
		if err := batch.Flush(); err != nil {
			return err
		}
	}

	return nil
}

// updateLargeTestDataForGC updates existing data to create stale entries in value log.
func updateLargeTestDataForGC(db *badger.DB, count int, valueSize int) error {
	batchSize := 500

	for start := 0; start < count; start += batchSize {
		end := start + batchSize
		if end > count {
			end = count
		}

		batch := db.NewWriteBatch()

		for i := start; i < end; i++ {
			key := []byte(fmt.Sprintf("gc_test_key_%06d", i))

			// Create updated large value (different from original)
			value := make([]byte, valueSize)
			for j := range value {
				value[j] = byte('Z' - ((i + j) % 26)) // Different pattern
			}

			// Add updated prefix
			prefix := fmt.Sprintf("UPDT_%06d:", i)
			copy(value[:len(prefix)], prefix)

			err := batch.Set(key, value)
			if err != nil {
				batch.Cancel()
				return err
			}
		}

		if err := batch.Flush(); err != nil {
			return err
		}
	}

	return nil
}

// deleteLargeTestDataForGC deletes test data to create garbage in value log.
func deleteLargeTestDataForGC(db *badger.DB, count int) error {
	batchSize := 500

	for start := 0; start < count; start += batchSize {
		end := start + batchSize
		if end > count {
			end = count
		}

		batch := db.NewWriteBatch()

		for i := start; i < end; i++ {
			key := []byte(fmt.Sprintf("gc_test_key_%06d", i))

			err := batch.Delete(key)
			if err != nil {
				batch.Cancel()
				return err
			}
		}

		if err := batch.Flush(); err != nil {
			return err
		}
	}

	return nil
}

// TestGarbageCollector_ForceValueLogGC creates conditions that definitely trigger value log GC.
func TestGarbageCollector_ForceValueLogGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_force_gc.db")

	// Create test configuration with very aggressive GC settings
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     30 * time.Millisecond, // Very short interval
		GCDiscardRatio: 0.01,                  // Very low threshold - GC almost everything
	}

	// Create logger with debug level
	logger := zerolog.New(zerolog.NewConsoleWriter()).Level(zerolog.DebugLevel).With().Timestamp().Logger()

	// Open database with settings optimized for forcing value log GC
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 1 << 20 // 1MB value log files (small for more files)
	dbOptions.ValueThreshold = 100       // Very low threshold - force almost everything to value log
	dbOptions.MemTableSize = 1 << 20     // 1MB memtable (small for more flushes)

	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("=== Phase 1: Creating initial large dataset ===")

	// Create initial dataset that will span multiple value log files
	const initialCount = 3000
	const valueSize = 1024 // 1KB values

	err = createLargeTestDataForGC(db, initialCount, valueSize)
	require.NoError(t, err)
	require.NoError(t, db.Sync())

	// Check database size before operations
	sizeBefore := getDatabaseSize(db)
	t.Logf("Database size after initial creation: %d bytes", sizeBefore)

	t.Log("=== Phase 2: Creating more data to force multiple value log files ===")

	// Create more data to ensure we have multiple value log files
	err = createMoreTestDataForGC(db, initialCount, initialCount+2000, valueSize)
	require.NoError(t, err)
	require.NoError(t, db.Sync())

	sizeAfterMore := getDatabaseSize(db)
	t.Logf("Database size after creating more data: %d bytes", sizeAfterMore)

	t.Log("=== Phase 3: Updating all data to create stale entries ===")

	// Update ALL data to create maximum stale entries
	err = updateAllTestDataForGC(db, initialCount+2000, valueSize)
	require.NoError(t, err)
	require.NoError(t, db.Sync())

	sizeAfterUpdate := getDatabaseSize(db)
	t.Logf("Database size after updates: %d bytes", sizeAfterUpdate)

	t.Log("=== Phase 4: Deleting majority of data ===")

	// Delete 80% of data to create lots of garbage
	deleteCount := int(float64(initialCount+2000) * 0.8)
	err = deleteLargeTestDataForGC(db, deleteCount)
	require.NoError(t, err)
	require.NoError(t, db.Sync())

	sizeAfterDelete := getDatabaseSize(db)
	t.Logf("Database size after deleting %d entries: %d bytes", deleteCount, sizeAfterDelete)

	t.Log("=== Phase 5: Running intensive garbage collection ===")

	// Create and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "force_test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let automatic GC run for a while
	time.Sleep(300 * time.Millisecond)

	// Run manual GC multiple times with detailed logging
	successfulGCRuns := 0
	noRewriteCount := 0

	for i := 0; i < 20; i++ {
		sizeBefore := getDatabaseSize(db)
		err = gc.RunGC()
		sizeAfter := getDatabaseSize(db)

		if err == nil {
			successfulGCRuns++
			spaceReclaimed := sizeBefore - sizeAfter
			t.Logf("GC run %d: SUCCESS - Reclaimed %d bytes (%.2f%% reduction)",
				i+1, spaceReclaimed, float64(spaceReclaimed)/float64(sizeBefore)*100)
		} else if errors.Is(err, badger.ErrNoRewrite) {
			noRewriteCount++
			t.Logf("GC run %d: No rewrite needed (size: %d bytes)", i+1, sizeAfter)
		} else {
			t.Errorf("GC run %d: Unexpected error: %v", i+1, err)
		}

		time.Sleep(20 * time.Millisecond)
	}

	finalSize := getDatabaseSize(db)
	totalSpaceReclaimed := sizeAfterDelete - finalSize

	t.Logf("=== GC Results Summary ===")
	t.Logf("Successful GC runs: %d", successfulGCRuns)
	t.Logf("No-rewrite responses: %d", noRewriteCount)
	t.Logf("Size before GC: %d bytes", sizeAfterDelete)
	t.Logf("Size after GC: %d bytes", finalSize)
	t.Logf("Total space reclaimed: %d bytes (%.2f%% reduction)",
		totalSpaceReclaimed, float64(totalSpaceReclaimed)/float64(sizeAfterDelete)*100)

	// Verify database integrity
	err = verifyDataIntegrity(db)
	require.NoError(t, err)

	// Assert that we had at least some successful GC runs
	require.Greater(t, successfulGCRuns, 0, "Expected at least one successful GC run")

	t.Log("✅ Force GC test completed successfully")
}

// Additional helper functions for advanced GC testing

// getDatabaseSize returns the approximate size of the database.
func getDatabaseSize(db *badger.DB) int64 {
	lsm, vlog := db.Size()
	return lsm + vlog
}

// createMoreTestDataForGC creates additional test data in a specific range.
func createMoreTestDataForGC(db *badger.DB, startIndex, endIndex, valueSize int) error {
	batchSize := 300

	for start := startIndex; start < endIndex; start += batchSize {
		end := start + batchSize
		if end > endIndex {
			end = endIndex
		}

		batch := db.NewWriteBatch()

		for i := start; i < end; i++ {
			key := []byte(fmt.Sprintf("gc_test_key_%06d", i))

			// Create large value
			value := make([]byte, valueSize)
			for j := range value {
				value[j] = byte('B' + ((i + j) % 25)) // Different pattern
			}

			prefix := fmt.Sprintf("MORE_%06d:", i)
			copy(value[:len(prefix)], prefix)

			err := batch.Set(key, value)
			if err != nil {
				batch.Cancel()
				return err
			}
		}

		if err := batch.Flush(); err != nil {
			return err
		}
	}

	return nil
}

// updateAllTestDataForGC updates all test data to create maximum stale entries.
func updateAllTestDataForGC(db *badger.DB, count int, valueSize int) error {
	batchSize := 400

	for start := 0; start < count; start += batchSize {
		end := start + batchSize
		if end > count {
			end = count
		}

		batch := db.NewWriteBatch()

		for i := start; i < end; i++ {
			key := []byte(fmt.Sprintf("gc_test_key_%06d", i))

			// Create completely different value
			value := make([]byte, valueSize)
			for j := range value {
				value[j] = byte('X' + ((i*2 + j*3) % 23)) // Very different pattern
			}

			prefix := fmt.Sprintf("FINAL_%06d:", i)
			copy(value[:len(prefix)], prefix)

			err := batch.Set(key, value)
			if err != nil {
				batch.Cancel()
				return err
			}
		}

		if err := batch.Flush(); err != nil {
			return err
		}
	}

	return nil
}

// TestGarbageCollector_RealValueLogGC creates a test that definitely uses value log and triggers GC.
func TestGarbageCollector_RealValueLogGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_real_vlog_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     100 * time.Millisecond,
		GCDiscardRatio: 0.1, // 10% threshold
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).Level(zerolog.InfoLevel).With().Timestamp().Logger()

	// Open database with settings that FORCE value log usage
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 4 << 20 // 4MB value log files
	dbOptions.ValueThreshold = 1         // Force ALL values into value log (even 1 byte)
	dbOptions.MemTableSize = 2 << 20     // 2MB memtable

	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("=== Creating large data that MUST go to value log ===")

	// Create data with very large values (10KB each)
	const numEntries = 1000
	const valueSize = 10 * 1024 // 10KB per value

	// Phase 1: Create initial data
	for i := 0; i < numEntries; i++ {
		key := []byte(fmt.Sprintf("vlog_key_%06d", i))
		value := make([]byte, valueSize)

		// Fill with recognizable pattern
		for j := range value {
			value[j] = byte('A' + (i % 26))
		}

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, value)
		})
		require.NoError(t, err)

		// Log progress
		if i%200 == 199 {
			t.Logf("Created %d entries", i+1)
		}
	}

	require.NoError(t, db.Sync())

	// Check size after creation
	lsm1, vlog1 := db.Size()
	t.Logf("After creation - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm1, vlog1, lsm1+vlog1)

	// Verify we actually have data in value log
	require.Greater(t, vlog1, int64(0), "Expected data in value log")

	t.Log("=== Updating data to create stale entries ===")

	// Phase 2: Update all data to create stale entries
	for i := 0; i < numEntries; i++ {
		key := []byte(fmt.Sprintf("vlog_key_%06d", i))
		value := make([]byte, valueSize)

		// Fill with different pattern
		for j := range value {
			value[j] = byte('Z' - (i % 26))
		}

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, value)
		})
		require.NoError(t, err)

		if i%200 == 199 {
			t.Logf("Updated %d entries", i+1)
		}
	}

	require.NoError(t, db.Sync())

	lsm2, vlog2 := db.Size()
	t.Logf("After updates - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm2, vlog2, lsm2+vlog2)

	t.Log("=== Deleting majority of data ===")

	// Phase 3: Delete 70% of data
	deleteCount := int(float64(numEntries) * 0.7)
	for i := 0; i < deleteCount; i++ {
		key := []byte(fmt.Sprintf("vlog_key_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Delete(key)
		})
		require.NoError(t, err)

		if i%200 == 199 {
			t.Logf("Deleted %d entries", i+1)
		}
	}

	require.NoError(t, db.Sync())

	lsm3, vlog3 := db.Size()
	t.Logf("After deletions - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm3, vlog3, lsm3+vlog3)

	t.Log("=== Running garbage collection ===")

	// Create and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "real_vlog_test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let automatic GC run
	time.Sleep(500 * time.Millisecond)

	// Run manual GC
	gcSuccessCount := 0
	gcNoRewriteCount := 0

	for i := 0; i < 15; i++ {
		_, vlogBefore := db.Size()
		err = gc.RunGC()
		_, vlogAfter := db.Size()

		if err == nil {
			gcSuccessCount++
			vlogReclaimed := vlogBefore - vlogAfter
			t.Logf("GC run %d: SUCCESS - VLog reclaimed %d bytes", i+1, vlogReclaimed)
		} else if errors.Is(err, badger.ErrNoRewrite) {
			gcNoRewriteCount++
			t.Logf("GC run %d: No rewrite needed", i+1)
		} else {
			t.Errorf("GC run %d: Unexpected error: %v", i+1, err)
		}

		time.Sleep(50 * time.Millisecond)
	}

	lsmFinal, vlogFinal := db.Size()
	t.Logf("Final - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsmFinal, vlogFinal, lsmFinal+vlogFinal)

	vlogReclaimed := vlog3 - vlogFinal
	t.Logf("=== Results ===")
	t.Logf("Successful GC runs: %d", gcSuccessCount)
	t.Logf("No-rewrite responses: %d", gcNoRewriteCount)
	t.Logf("Value log space reclaimed: %d bytes (%.1f%%)",
		vlogReclaimed, float64(vlogReclaimed)/float64(vlog3)*100)

	// Verify remaining data is accessible
	remainingCount := 0
	err = db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			remainingCount++
		}
		return nil
	})
	require.NoError(t, err)

	expectedRemaining := numEntries - deleteCount
	t.Logf("Remaining entries: %d (expected: %d)", remainingCount, expectedRemaining)
	require.Equal(t, expectedRemaining, remainingCount, "Unexpected number of remaining entries")

	// Assert that we had meaningful GC activity
	require.Greater(t, gcSuccessCount, 0, "Expected at least one successful GC run")
	require.Greater(t, vlogReclaimed, int64(0), "Expected some value log space to be reclaimed")

	t.Log("✅ Real value log GC test completed successfully")
}

// TestGarbageCollector_ExtremeValueLogGC creates extreme conditions to force value log usage and GC.
func TestGarbageCollector_ExtremeValueLogGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_extreme_vlog_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     50 * time.Millisecond,
		GCDiscardRatio: 0.05, // Very aggressive - 5% threshold
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).Level(zerolog.InfoLevel).With().Timestamp().Logger()

	// Open database with extreme settings to force value log usage
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 2 << 20 // 2MB value log files (small for more files)
	dbOptions.ValueThreshold = 0         // Force EVERYTHING into value log
	dbOptions.MemTableSize = 64 << 10    // 64KB memtable (very small to force frequent flushes)
	dbOptions.NumMemtables = 2           // Only 2 memtables

	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("=== Creating massive data to overwhelm memtable and force value log ===")

	// Create massive data that will definitely overflow memtable
	const numEntries = 500
	const valueSize = 50 * 1024 // 50KB per value = 25MB total

	// Phase 1: Create initial massive dataset
	for i := 0; i < numEntries; i++ {
		key := []byte(fmt.Sprintf("extreme_key_%06d", i))
		value := make([]byte, valueSize)

		// Fill with pattern
		pattern := fmt.Sprintf("EXTREME_DATA_%06d_", i)
		for j := 0; j < len(value); j += len(pattern) {
			copy(value[j:], pattern)
		}

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, value)
		})
		require.NoError(t, err)

		// Force sync every 50 entries to ensure data goes to disk
		if i%50 == 49 {
			require.NoError(t, db.Sync())
			lsm, vlog := db.Size()
			t.Logf("After %d entries - LSM: %d bytes, VLog: %d bytes", i+1, lsm, vlog)
		}
	}

	require.NoError(t, db.Sync())

	// Check size after creation
	lsm1, vlog1 := db.Size()
	t.Logf("After creation - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm1, vlog1, lsm1+vlog1)

	// This time we should definitely have data in value log
	if vlog1 == 0 {
		t.Logf("WARNING: Still no data in value log. LSM tree size: %d bytes", lsm1)
		t.Log("This suggests BadgerDB is keeping everything in LSM tree despite our settings")

		// Let's try to understand why by checking if we can force a value log entry
		testKey := []byte("force_vlog_test")
		testValue := make([]byte, 1024*1024) // 1MB value
		for i := range testValue {
			testValue[i] = byte('X')
		}

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(testKey, testValue)
		})
		require.NoError(t, err)
		require.NoError(t, db.Sync())

		lsm2, vlog2 := db.Size()
		t.Logf("After 1MB test entry - LSM: %d bytes, VLog: %d bytes", lsm2, vlog2)

		if vlog2 > vlog1 {
			t.Log("SUCCESS: Large entry forced data into value log")
		} else {
			t.Log("STILL NO VALUE LOG: Even 1MB entry stayed in LSM tree")
			// Continue with test anyway to verify GC behavior
		}
	}

	t.Log("=== Updating data to create stale entries ===")

	// Phase 2: Update data to create stale entries
	for i := 0; i < numEntries/2; i++ { // Update half the data
		key := []byte(fmt.Sprintf("extreme_key_%06d", i))
		value := make([]byte, valueSize)

		// Different pattern
		pattern := fmt.Sprintf("UPDATED_DATA_%06d_", i)
		for j := 0; j < len(value); j += len(pattern) {
			copy(value[j:], pattern)
		}

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, value)
		})
		require.NoError(t, err)

		if i%50 == 49 {
			require.NoError(t, db.Sync())
		}
	}

	require.NoError(t, db.Sync())

	lsm3, vlog3 := db.Size()
	t.Logf("After updates - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm3, vlog3, lsm3+vlog3)

	t.Log("=== Deleting data to create garbage ===")

	// Phase 3: Delete significant portion
	deleteCount := numEntries * 3 / 4 // Delete 75%
	for i := 0; i < deleteCount; i++ {
		key := []byte(fmt.Sprintf("extreme_key_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Delete(key)
		})
		require.NoError(t, err)

		if i%50 == 49 {
			require.NoError(t, db.Sync())
		}
	}

	require.NoError(t, db.Sync())

	lsm4, vlog4 := db.Size()
	t.Logf("After deletions - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsm4, vlog4, lsm4+vlog4)

	t.Log("=== Running intensive garbage collection ===")

	// Create and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "extreme_test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let automatic GC run
	time.Sleep(300 * time.Millisecond)

	// Run manual GC with detailed tracking
	gcSuccessCount := 0
	gcNoRewriteCount := 0
	totalVlogReclaimed := int64(0)

	for i := 0; i < 20; i++ {
		_, vlogBefore := db.Size()
		err = gc.RunGC()
		_, vlogAfter := db.Size()

		if err == nil {
			gcSuccessCount++
			vlogReclaimed := vlogBefore - vlogAfter
			totalVlogReclaimed += vlogReclaimed
			t.Logf("GC run %d: SUCCESS - VLog reclaimed %d bytes", i+1, vlogReclaimed)
		} else if errors.Is(err, badger.ErrNoRewrite) {
			gcNoRewriteCount++
			t.Logf("GC run %d: No rewrite needed", i+1)
		} else {
			t.Errorf("GC run %d: Unexpected error: %v", i+1, err)
		}

		time.Sleep(30 * time.Millisecond)
	}

	lsmFinal, vlogFinal := db.Size()
	t.Logf("Final - LSM: %d bytes, VLog: %d bytes, Total: %d bytes", lsmFinal, vlogFinal, lsmFinal+vlogFinal)

	t.Logf("=== Extreme GC Results ===")
	t.Logf("Successful GC runs: %d", gcSuccessCount)
	t.Logf("No-rewrite responses: %d", gcNoRewriteCount)
	t.Logf("Total value log space reclaimed: %d bytes", totalVlogReclaimed)

	if vlog4 > 0 {
		reductionPercent := float64(totalVlogReclaimed) / float64(vlog4) * 100
		t.Logf("Value log reduction: %.1f%%", reductionPercent)
	}

	// Verify remaining data
	remainingCount := 0
	err = db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			remainingCount++
		}
		return nil
	})
	require.NoError(t, err)

	expectedRemaining := numEntries - deleteCount
	t.Logf("Remaining entries: %d (expected: %d)", remainingCount, expectedRemaining)

	// The test is successful if:
	// 1. GC ran without errors
	// 2. Database integrity is maintained
	// 3. We can read remaining data

	t.Log("✅ Extreme value log GC test completed")
	t.Logf("Summary: GC ran %d times successfully, %d times with no-rewrite", gcSuccessCount, gcNoRewriteCount)

	if gcSuccessCount > 0 {
		t.Log("🎉 SUCCESS: Garbage collection actually ran and reclaimed space!")
	} else if gcNoRewriteCount > 0 {
		t.Log("ℹ️  INFO: GC ran but found no space to reclaim (this is also valid behavior)")
	} else {
		t.Log("⚠️  WARNING: No GC activity detected")
	}
}

// TestGarbageCollector_RecommendedApproach demonstrates the recommended approach for testing GC.
// This test focuses on verifying that GC runs without errors rather than forcing value log usage.
func TestGarbageCollector_RecommendedApproach(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_recommended_gc.db")

	// Create realistic configuration (similar to production)
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     100 * time.Millisecond,
		GCDiscardRatio: 0.5, // 50% threshold (realistic)
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).Level(zerolog.InfoLevel).With().Timestamp().Logger()

	// Use default BadgerDB settings (realistic for production)
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("=== Testing GC with realistic data patterns ===")

	// Create realistic data (similar to scan results)
	const numScans = 100
	for i := 0; i < numScans; i++ {
		scanData := createRealisticScanData(i)
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, scanData)
		})
		require.NoError(t, err)
	}

	// Update some data (realistic pattern)
	for i := 0; i < numScans/2; i++ {
		scanData := createRealisticScanData(i + 1000) // Different data
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, scanData)
		})
		require.NoError(t, err)
	}

	// Delete some old data (realistic cleanup)
	for i := 0; i < numScans/4; i++ {
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Delete(key)
		})
		require.NoError(t, err)
	}

	require.NoError(t, db.Sync())

	t.Log("=== Testing garbage collector functionality ===")

	// Create and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "recommended_test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let automatic GC run
	time.Sleep(300 * time.Millisecond)

	// Test manual GC runs
	gcAttempts := 0
	gcSuccesses := 0
	gcNoRewrites := 0

	for i := 0; i < 10; i++ {
		gcAttempts++
		err = gc.RunGC()

		if err == nil {
			gcSuccesses++
			t.Logf("GC attempt %d: SUCCESS", i+1)
		} else if errors.Is(err, badger.ErrNoRewrite) {
			gcNoRewrites++
			t.Logf("GC attempt %d: No rewrite needed (normal)", i+1)
		} else {
			t.Errorf("GC attempt %d: Unexpected error: %v", i+1, err)
		}

		time.Sleep(20 * time.Millisecond)
	}

	t.Logf("=== Recommended Test Results ===")
	t.Logf("GC attempts: %d", gcAttempts)
	t.Logf("GC successes: %d", gcSuccesses)
	t.Logf("GC no-rewrites: %d", gcNoRewrites)

	// Verify database integrity
	remainingCount := 0
	err = db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			remainingCount++

			// Verify we can read the data
			item := it.Item()
			err := item.Value(func(val []byte) error {
				// Just verify we can read it
				return nil
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", string(item.Key()), err)
			}
		}
		return nil
	})
	require.NoError(t, err)

	expectedRemaining := numScans - numScans/4 // Total minus deleted
	t.Logf("Remaining entries: %d (expected: %d)", remainingCount, expectedRemaining)
	require.Equal(t, expectedRemaining, remainingCount)

	// Success criteria:
	// 1. GC runs without fatal errors
	// 2. Database integrity is maintained
	// 3. Data can be read after GC operations

	require.Greater(t, gcAttempts, 0, "Should have attempted GC")
	require.Equal(t, 0, gcAttempts-(gcSuccesses+gcNoRewrites), "All GC attempts should succeed or return no-rewrite")

	t.Log("✅ Recommended GC test completed successfully")
	t.Log("📝 Key insight: GC success is measured by error-free operation, not space reclamation")
	t.Log("📝 BadgerDB GC is designed to run safely even when no cleanup is needed")
}

// createRealisticScanData creates data similar to actual scan results.
func createRealisticScanData(scanID int) []byte {
	// Simulate realistic scan data structure
	data := map[string]interface{}{
		"scan_id":        scanID,
		"timestamp":      time.Now().Unix(),
		"status":         "completed",
		"multicast_cidr": "*********/24",
		"port":           1234,
		"streams": []map[string]interface{}{
			{
				"address": "*********:1234",
				"clients": 5,
				"metrics": map[string]interface{}{
					"received_ts_packets": scanID * 1000,
					"received_bytes":      scanID * 10000,
					"present_duration":    scanID * 60,
					"receiving_duration":  scanID * 50,
				},
			},
		},
	}

	jsonData, _ := json.Marshal(data)
	return jsonData
}

// Helper function to verify data integrity.
func verifyDataIntegrity(db *badger.DB) error {
	return db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		opts.PrefetchSize = 10
		it := txn.NewIterator(opts)
		defer it.Close()

		count := 0
		for it.Rewind(); it.Valid(); it.Next() {
			item := it.Item()
			key := item.Key()

			err := item.Value(func(val []byte) error {
				// Just verify we can read the value
				var data map[string]interface{}
				return json.Unmarshal(val, &data)
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", string(key), err)
			}
			count++
		}

		// We should have some remaining data after deletions
		if count == 0 {
			return fmt.Errorf("no data found in database")
		}

		return nil
	})
}

func TestGarbageCollectorConcurrency(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrency_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     30 * time.Millisecond, // Very short interval for testing
		GCDiscardRatio: 0.1,                   // Low threshold for testing
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Initialize and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Run concurrent operations: write data while GC is running
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Millisecond)
	defer cancel()

	const numWorkers = 3
	errors := make(chan error, numWorkers)

	// Start concurrent workers that write data
	for i := range numWorkers {
		go func(workerID int) {
			err := writeDataConcurrently(ctx, db, workerID)
			errors <- err
		}(i)
	}

	// Wait for all workers to complete
	for range numWorkers {
		err := <-errors
		require.NoError(t, err, "Worker should complete without errors")
	}

	// Verify database integrity after concurrent operations
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

// Helper function to write data concurrently.
func writeDataConcurrently(ctx context.Context, db *badger.DB, workerID int) error {
	counter := 0

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			err := db.Update(func(txn *badger.Txn) error {
				key := []byte(fmt.Sprintf("worker_%d_key_%d", workerID, counter))
				value := map[string]interface{}{
					"worker_id": workerID,
					"counter":   counter,
					"timestamp": time.Now().Unix(),
				}

				valueBytes, err := json.Marshal(value)
				if err != nil {
					return err
				}

				return txn.Set(key, valueBytes)
			})
			if err != nil {
				return err
			}
			counter++
			time.Sleep(10 * time.Millisecond) // Small delay to avoid overwhelming the DB
		}
	}
}
