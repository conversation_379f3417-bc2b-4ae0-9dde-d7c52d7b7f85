package badgerdb_test

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestNewGarbageCollector(t *testing.T) {
	t.<PERSON>llel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NotNil(t, gc)
}

func TestGarbageCollector_StartStop(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test start
	err = gc.Start()
	require.NoError(t, err)

	// Test stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_RunGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test manual GC run
	err = gc.RunGC()
	require.NoError(t, err)
}

func TestGarbageCollector_DoubleStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Start first time
	err = gc.Start()
	require.NoError(t, err)

	// Try to start again - should fail
	err = gc.Start()
	require.Error(t, err)
	require.Contains(t, err.Error(), "already running")

	// Stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_StopWithoutStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Stop without starting - should not error
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_ActualGarbageCollection(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_gc.db")

	// Create test configuration with aggressive GC settings
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     50 * time.Millisecond, // Very short interval for testing
		GCDiscardRatio: 0.1,                   // Low threshold for testing
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database with settings that encourage value log files
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 1 << 20 // 1MB value log files
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create large amounts of data to force value log usage
	err = createLargeTestData(db, 1000, 1024) // 1000 entries with 1KB each
	require.NoError(t, err)

	// Update the data to create stale entries
	err = updateTestData(db, 1000, 1024)
	require.NoError(t, err)

	// Delete some data to create more garbage
	err = deleteTestData(db, 500)
	require.NoError(t, err)

	// Create garbage collector and start it
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let the garbage collector run for a while
	time.Sleep(200 * time.Millisecond)

	// Manually trigger GC to ensure it runs
	err = gc.RunGC()
	require.NoError(t, err)

	// Verify database is still functional
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

// Helper function to create large test data.
func createLargeTestData(db *badger.DB, count int, valueSize int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))
			
			// Create large value
			value := map[string]interface{}{
				"id":        i,
				"data":      generateLargeString(valueSize),
				"timestamp": time.Now().Unix(),
			}

			valueBytes, err := json.Marshal(value)
			if err != nil {
				return err
			}

			if err := txn.Set(key, valueBytes); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to update test data (creates stale entries).
func updateTestData(db *badger.DB, count int, valueSize int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))
			
			// Create updated large value
			value := map[string]interface{}{
				"id":        i,
				"data":      generateLargeString(valueSize) + "_updated",
				"timestamp": time.Now().Unix(),
			}

			valueBytes, err := json.Marshal(value)
			if err != nil {
				return err
			}

			if err := txn.Set(key, valueBytes); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to delete test data.
func deleteTestData(db *badger.DB, count int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("large_key_%d", i))
			if err := txn.Delete(key); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to generate large string.
func generateLargeString(size int) string {
	data := make([]byte, size)
	for i := range data {
		data[i] = byte('A' + (i % 26))
	}
	return string(data)
}

// Helper function to verify data integrity.
func verifyDataIntegrity(db *badger.DB) error {
	return db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		opts.PrefetchSize = 10
		it := txn.NewIterator(opts)
		defer it.Close()

		count := 0
		for it.Rewind(); it.Valid(); it.Next() {
			item := it.Item()
			key := item.Key()
			
			err := item.Value(func(val []byte) error {
				// Just verify we can read the value
				var data map[string]interface{}
				return json.Unmarshal(val, &data)
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", string(key), err)
			}
			count++
		}
		
		// We should have some remaining data after deletions
		if count == 0 {
			return fmt.Errorf("no data found in database")
		}
		
		return nil
	})
}

func TestGarbageCollectorConcurrency(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrency_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     30 * time.Millisecond, // Very short interval for testing
		GCDiscardRatio: 0.1,                   // Low threshold for testing
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Initialize and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Run concurrent operations: write data while GC is running
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Millisecond)
	defer cancel()

	const numWorkers = 3
	errors := make(chan error, numWorkers)

	// Start concurrent workers that write data
	for i := range numWorkers {
		go func(workerID int) {
			err := writeDataConcurrently(ctx, db, workerID)
			errors <- err
		}(i)
	}

	// Wait for all workers to complete
	for range numWorkers {
		err := <-errors
		require.NoError(t, err, "Worker should complete without errors")
	}

	// Verify database integrity after concurrent operations
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

// Helper function to write data concurrently.
func writeDataConcurrently(ctx context.Context, db *badger.DB, workerID int) error {
	counter := 0

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			err := db.Update(func(txn *badger.Txn) error {
				key := []byte(fmt.Sprintf("worker_%d_key_%d", workerID, counter))
				value := map[string]interface{}{
					"worker_id": workerID,
					"counter":   counter,
					"timestamp": time.Now().Unix(),
				}

				valueBytes, err := json.Marshal(value)
				if err != nil {
					return err
				}

				return txn.Set(key, valueBytes)
			})
			if err != nil {
				return err
			}
			counter++
			time.Sleep(10 * time.Millisecond) // Small delay to avoid overwhelming the DB
		}
	}
}
