package badgerdb_test

import (
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/relay/infrastructure/persistence/badgerdb"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestNewGarbageCollector(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	conf := config.RelayConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = time.Hour
	conf.Database.GCDiscardRatio = 0.5

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := badgerdb.NewGarbageCollector(db, logger, conf)
	require.NotNil(t, gc)
}

func TestGarbageCollector_StartStop(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	conf := config.RelayConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = time.Hour
	conf.Database.GCDiscardRatio = 0.5

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := badgerdb.NewGarbageCollector(db, logger, conf)

	// Test start
	err = gc.Start()
	require.NoError(t, err)

	// Test stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_RunGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	conf := config.RelayConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = time.Hour
	conf.Database.GCDiscardRatio = 0.5

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := badgerdb.NewGarbageCollector(db, logger, conf)

	// Test manual GC run
	err = gc.RunGC()
	require.NoError(t, err)
}

func TestGarbageCollector_MultipleStartStop(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	conf := config.RelayConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = time.Hour
	conf.Database.GCDiscardRatio = 0.5

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := badgerdb.NewGarbageCollector(db, logger, conf)

	// Test multiple start/stop cycles
	for range 3 {
		err = gc.Start()
		require.NoError(t, err)

		err = gc.Stop()
		require.NoError(t, err)
	}
}
