package badgerdb

import (
	"errors"
	"sync"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
)

// GarbageCollector manages automatic garbage collection for BadgerDB.
type GarbageCollector struct {
	db             *badger.DB
	log            zerolog.Logger
	gcInterval     time.Duration
	gcDiscardRatio float64
	stopCh         chan struct{}
	running        bool
	mutex          sync.Mutex
}

// NewGarbageCollector creates a new GarbageCollector instance.
func NewGarbageCollector(db *badger.DB, log zerolog.Logger, config config.RelayConfig) *GarbageCollector {
	return &GarbageCollector{
		db:             db,
		log:            log.With().Str("component", "relay_garbage_collector").Logger(),
		gcInterval:     config.Database.GCInterval,
		gcDiscardRatio: config.Database.GCDiscardRatio,
	}
}

// Start begins the garbage collection process.
func (gc *GarbageCollector) Start() error {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	if gc.running {
		return errors.New("garbage collector already running")
	}

	gc.stopCh = make(chan struct{})
	gc.running = true

	gc.log.Info().
		Dur("interval", gc.gcInterval).
		Float64("discard_ratio", gc.gcDiscardRatio).
		Msg("Starting garbage collector")

	go gc.run()

	return nil
}

// Stop terminates the garbage collection process.
func (gc *GarbageCollector) Stop() error {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	if !gc.running {
		return nil
	}

	gc.log.Info().Msg("Stopping garbage collector")
	close(gc.stopCh)
	gc.running = false
	gc.log.Info().Msg("Garbage collector stopped")

	return nil
}

// RunGC executes a single garbage collection cycle.
func (gc *GarbageCollector) RunGC() error {
	gc.log.Debug().Msg("Running value log garbage collection")

	err := gc.db.RunValueLogGC(gc.gcDiscardRatio)
	if errors.Is(err, badger.ErrNoRewrite) {
		// This is expected when no GC is needed
		gc.log.Debug().Msg("No value log garbage collection needed")
		return nil
	}

	return err
}

// getStopChannel safely retrieves the stop channel.
func (gc *GarbageCollector) getStopChannel() <-chan struct{} {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()
	return gc.stopCh
}

// run is the main garbage collection loop.
func (gc *GarbageCollector) run() {
	ticker := time.NewTicker(gc.gcInterval)
	defer ticker.Stop()

	stopCh := gc.getStopChannel()

	for {
		select {
		case <-stopCh:
			gc.log.Debug().Msg("Garbage collector received stop signal")
			return
		case <-ticker.C:
			if err := gc.RunGC(); err != nil {
				gc.log.Error().Err(err).Msg("Garbage collection failed")
			}
		}
	}
}
