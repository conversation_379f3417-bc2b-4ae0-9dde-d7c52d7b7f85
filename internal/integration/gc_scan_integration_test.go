package integration_test

import (
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	"git.moderntv.eu/multicast-probe/internal/management/infrastructure/persistence/badgerdb"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	badger "github.com/dgraph-io/badger/v4"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestGarbageCollectorWithScans(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_gc_scans.db")

	// Create test configuration with aggressive GC settings
	conf := config.ManagementConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = 100 * time.Millisecond // Very short interval for testing
	conf.Database.GCDiscardRatio = 0.1                // Low threshold for testing

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database with settings that encourage value log files
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 1 << 20 // 1MB value log files
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create scan repository
	scanRepo := badgerdb.NewScanRepository(logger, conf, db)

	// Create garbage collector and start it
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, conf.Database, "management")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Phase 1: Create many scans with large data
	t.Log("Phase 1: Creating scans with large data")
	scanIDs := make([]string, 0, 100)
	for i := 0; i < 100; i++ {
		scan := createLargeScan(i)
		err := scanRepo.StoreScan(scan)
		require.NoError(t, err)
		scanIDs = append(scanIDs, scan.ID.String())
	}

	// Verify scans were created
	scans, err := scanRepo.GetScans()
	require.NoError(t, err)
	require.Len(t, scans, 100)
	t.Logf("Created %d scans", len(scans))

	// Phase 2: Update scans (creates stale data)
	t.Log("Phase 2: Updating scans to create stale data")
	for i, scanID := range scanIDs {
		scan, err := scanRepo.GetScan(scanID)
		require.NoError(t, err)

		// Update scan with new data
		scan.Status = entity.ScanStatusCompleted
		scan.Streams = createLargeStreams(i + 100) // Different data to create fragmentation

		err = scanRepo.StoreScan(scan)
		require.NoError(t, err)
	}

	// Phase 3: Delete half of the scans (creates garbage)
	t.Log("Phase 3: Deleting half of scans to create garbage")
	deletedCount := 0
	for i, scanID := range scanIDs {
		if i%2 == 0 { // Delete every other scan
			err := scanRepo.DeleteScan(scanID)
			require.NoError(t, err)
			deletedCount++
		}
	}
	t.Logf("Deleted %d scans", deletedCount)

	// Verify deletions
	scans, err = scanRepo.GetScans()
	require.NoError(t, err)
	require.Len(t, scans, 50) // Should have 50 remaining
	t.Logf("Remaining scans: %d", len(scans))

	// Phase 4: Let garbage collector run and monitor
	t.Log("Phase 4: Monitoring garbage collection")

	// Wait for several GC cycles
	time.Sleep(500 * time.Millisecond)

	// Manually trigger GC to ensure it runs
	err = gc.RunGC()
	require.NoError(t, err)

	// Phase 5: Verify database integrity after GC
	t.Log("Phase 5: Verifying database integrity")

	// Verify remaining scans are still accessible
	scans, err = scanRepo.GetScans()
	require.NoError(t, err)
	require.Len(t, scans, 50)

	// Verify we can read individual scans
	for _, scan := range scans {
		retrievedScan, err := scanRepo.GetScan(scan.ID.String())
		require.NoError(t, err)
		require.Equal(t, scan.ID, retrievedScan.ID)
		require.Equal(t, scan.Status, retrievedScan.Status)
		require.NotEmpty(t, retrievedScan.Streams)
	}

	t.Log("✅ Garbage collection test completed successfully")
	t.Logf("Final state: %d scans remaining, GC ran without errors", len(scans))
}

func TestGarbageCollectorConcurrentScansAndGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrent_gc_scans.db")

	// Create test configuration with very aggressive GC settings
	conf := config.ManagementConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = 50 * time.Millisecond // Very short interval
	conf.Database.GCDiscardRatio = 0.05              // Very low threshold

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	dbOptions.ValueLogFileSize = 1 << 20 // 1MB value log files (minimum allowed size)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create scan repository
	scanRepo := badgerdb.NewScanRepository(logger, conf, db)

	// Create garbage collector and start it
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, conf.Database, "management")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Run concurrent operations: create, update, delete scans while GC is running
	t.Log("Running concurrent scan operations with active garbage collection")

	const numIterations = 20
	const scansPerIteration = 10

	for iteration := 0; iteration < numIterations; iteration++ {
		// Create scans
		scanIDs := make([]string, 0, scansPerIteration)
		for i := 0; i < scansPerIteration; i++ {
			scan := createLargeScan(iteration*scansPerIteration + i)
			err := scanRepo.StoreScan(scan)
			require.NoError(t, err)
			scanIDs = append(scanIDs, scan.ID.String())
		}

		// Update some scans
		for i, scanID := range scanIDs {
			if i%2 == 0 {
				scan, err := scanRepo.GetScan(scanID)
				require.NoError(t, err)
				scan.Status = entity.ScanStatusCompleted
				err = scanRepo.StoreScan(scan)
				require.NoError(t, err)
			}
		}

		// Delete some scans
		for i, scanID := range scanIDs {
			if i%3 == 0 {
				err := scanRepo.DeleteScan(scanID)
				require.NoError(t, err)
			}
		}

		// Small delay to let GC run
		time.Sleep(10 * time.Millisecond)
	}

	// Final verification
	scans, err := scanRepo.GetScans()
	require.NoError(t, err)
	t.Logf("Final scan count: %d", len(scans))

	// Verify database integrity
	for _, scan := range scans {
		retrievedScan, err := scanRepo.GetScan(scan.ID.String())
		require.NoError(t, err)
		require.Equal(t, scan.ID, retrievedScan.ID)
	}

	t.Log("✅ Concurrent operations with GC completed successfully")
}

// Helper function to create a scan with large data
func createLargeScan(index int) entity.Scan {
	scanID := uuid.New()

	return entity.Scan{
		ID: scanID,
		Parameters: value.ScanParameters{
			MulticastCIDR: "*********/24",
			Port:          1234,
		},
		CreatedAt: value.NewUNIXTime(time.Now()),
		Status:    entity.ScanStatusQueued,
		Streams:   createLargeStreams(index),
	}
}

// Helper function to create streams with large data
func createLargeStreams(index int) []entity.Stream {
	streams := make([]entity.Stream, 0, 10)

	for i := 0; i < 10; i++ {
		address, err := value.ParseAddress("239.1.1."+string(rune('1'+i)), 1234)
		if err != nil {
			// Fallback to a valid address
			address = value.MustParseAddress("*********", 1234)
		}

		stream := entity.Stream{
			Address: address,
			Clients: i + 1,
			Metrics: value.StreamMetrics{
				ReceivedTSPackets: int64(index*1000 + i*100),
				ReceivedBytes:     int64(index*10000 + i*1000),
				PresentDuration:   int64(index*60 + i*10),
				ReceivingDuration: int64(index*50 + i*8),
			},
			Programs: createLargePrograms(index, i),
		}
		streams = append(streams, stream)
	}

	return streams
}

// Helper function to create programs with large data
func createLargePrograms(scanIndex, streamIndex int) []entity.Program {
	programs := make([]entity.Program, 0, 5)

	for i := 0; i < 5; i++ {
		program := entity.Program{
			ProgramID: i + 1,
			Title:     generateLargeString(100, scanIndex, streamIndex, i), // 100 char title
			Provider:  generateLargeString(500, scanIndex, streamIndex, i), // 500 char provider
			Tracks:    createLargeTracks(scanIndex, streamIndex, i),
		}
		programs = append(programs, program)
	}

	return programs
}

// Helper function to create tracks with large data
func createLargeTracks(scanIndex, streamIndex, programIndex int) []entity.Track {
	tracks := make([]entity.Track, 0, 3)

	for i := 0; i < 3; i++ {
		track := entity.Track{
			PID:         100 + i,
			Type:        i + 1,                                                               // int type, not uint8
			Description: generateLargeString(200, scanIndex, streamIndex, programIndex*10+i), // 200 char description
		}
		tracks = append(tracks, track)
	}

	return tracks
}

// Helper function to generate large strings with unique content
func generateLargeString(size, seed1, seed2, seed3 int) string {
	data := make([]byte, size)
	for i := range data {
		// Create pseudo-random but deterministic content
		char := byte('A' + ((seed1*31 + seed2*17 + seed3*7 + i) % 26))
		data[i] = char
	}
	return string(data)
}
