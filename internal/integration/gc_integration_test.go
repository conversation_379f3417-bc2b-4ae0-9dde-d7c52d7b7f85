package integration_test

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestManagementGCIntegration(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	// Create a temporary directory for the test database
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_management_gc.db")

	// Create test configuration
	conf := config.ManagementConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = 50 * time.Millisecond // Very short interval for testing
	conf.Database.GCDiscardRatio = 0.1               // Low threshold for testing

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create some test data to ensure GC has something to work with
	err = createTestData(db, 1000)
	require.NoError(t, err)

	// Initialize and start garbage collector
	garbageCollector := sharedBadgerDB.NewGarbageCollector(db, logger, conf.Database, "management")
	require.NoError(t, garbageCollector.Start())

	// Let the garbage collector run for a while
	time.Sleep(200 * time.Millisecond)

	// Verify garbage collector is running
	// We can't directly verify GC effectiveness without creating a lot of data,
	// but we can verify the collector is operating without errors
	require.NoError(t, garbageCollector.RunGC())

	// Stop the garbage collector
	require.NoError(t, garbageCollector.Stop())

	// Verify database is still accessible and data integrity is maintained
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

func TestRelayGCIntegration(t *testing.T) {
	t.Parallel()

	// Create a temporary directory for the test database
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_relay_gc.db")

	// Create test configuration
	conf := config.RelayConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = 50 * time.Millisecond // Very short interval for testing
	conf.Database.GCDiscardRatio = 0.1               // Low threshold for testing

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create some test data to ensure GC has something to work with
	err = createTestData(db, 100)
	require.NoError(t, err)

	// Initialize and start garbage collector
	garbageCollector := sharedBadgerDB.NewGarbageCollector(db, logger, conf.Database, "relay")
	require.NoError(t, garbageCollector.Start())

	// Let the garbage collector run for a while
	time.Sleep(200 * time.Millisecond)

	// Verify garbage collector is running
	// We can't directly verify GC effectiveness without creating a lot of data,
	// but we can verify the collector is operating without errors
	require.NoError(t, garbageCollector.RunGC())

	// Stop the garbage collector
	require.NoError(t, garbageCollector.Stop())

	// Verify database is still accessible and data integrity is maintained
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

func TestGarbageCollectorConcurrency(t *testing.T) {
	t.Parallel()

	// Create a temporary directory for the test database
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrency_gc.db")

	// Create test configuration
	conf := config.ManagementConfig{}
	conf.Database.Path = dbPath
	conf.Database.GCInterval = 30 * time.Millisecond // Very short interval for testing
	conf.Database.GCDiscardRatio = 0.1               // Low threshold for testing

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Initialize and start garbage collector
	garbageCollector := sharedBadgerDB.NewGarbageCollector(db, logger, conf.Database, "management")
	require.NoError(t, garbageCollector.Start())
	defer func() {
		require.NoError(t, garbageCollector.Stop())
	}()

	// Run concurrent operations: write data while GC is running
	ctx, cancel := context.WithTimeout(t.Context(), 300*time.Millisecond)
	defer cancel()

	const numWorkers = 3
	errors := make(chan error, numWorkers)

	// Start concurrent workers that write data
	for i := range numWorkers {
		go func(workerID int) {
			err := writeDataConcurrently(ctx, db, workerID)
			errors <- err
		}(i)
	}

	// Wait for all workers to complete
	for range numWorkers {
		err := <-errors
		require.NoError(t, err, "Worker should complete without errors")
	}

	// Verify database integrity after concurrent operations
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

// Helper function to create test data in the database.
func createTestData(db *badger.DB, count int) error {
	return db.Update(func(txn *badger.Txn) error {
		for i := range count {
			key := []byte(fmt.Sprintf("test_key_%d", i))
			value := map[string]interface{}{
				"id":        i,
				"data":      fmt.Sprintf("test_data_%d", i),
				"timestamp": time.Now().Unix(),
			}

			valueBytes, err := json.Marshal(value)
			if err != nil {
				return err
			}

			if err := txn.Set(key, valueBytes); err != nil {
				return err
			}
		}
		return nil
	})
}

// Helper function to verify data integrity.
func verifyDataIntegrity(db *badger.DB) error {
	return db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		opts.PrefetchValues = false
		it := txn.NewIterator(opts)
		defer it.Close()

		keyCount := 0
		for it.Rewind(); it.Valid(); it.Next() {
			item := it.Item()
			key := item.Key()

			// Verify we can read the value
			err := item.Value(func(val []byte) error {
				var data map[string]interface{}
				return json.Unmarshal(val, &data)
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", key, err)
			}

			keyCount++
		}

		// We expect at least some data to be present
		if keyCount == 0 {
			return errors.New("no data found in database")
		}

		return nil
	})
}

// Helper function to write data concurrently.
func writeDataConcurrently(ctx context.Context, db *badger.DB, workerID int) error {
	counter := 0

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			err := db.Update(func(txn *badger.Txn) error {
				key := []byte(fmt.Sprintf("worker_%d_key_%d", workerID, counter))
				value := map[string]interface{}{
					"worker_id": workerID,
					"counter":   counter,
					"timestamp": time.Now().Unix(),
				}

				valueBytes, err := json.Marshal(value)
				if err != nil {
					return err
				}

				return txn.Set(key, valueBytes)
			})
			if err != nil {
				return err
			}

			counter++

			// Small delay to avoid overwhelming the database
			time.Sleep(10 * time.Millisecond)
		}
	}
}
