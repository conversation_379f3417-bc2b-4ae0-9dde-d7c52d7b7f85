#!/usr/bin/env python3
import subprocess
import time
import requests
import json
import os
import signal
import threading
import sys
import uuid
import random
import string
from pathlib import Path

def cleanup_databases():
    """Clean up test databases"""
    import shutil
    test_data_path = Path("test_data")
    if test_data_path.exists():
        shutil.rmtree(test_data_path)
    print("Cleaned up test databases")

def run_service(binary, config, service_name):
    """Run a service with the given config"""
    print(f"Starting {service_name} with config {config}")
    process = subprocess.Popen(
        [f"./bin/{binary}", "-config", config],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    return process

def check_service_health(url, service_name):
    """Check if service is healthy"""
    try:
        response = requests.get(f"{url}/_/apidoc", timeout=5)
        if response.status_code == 200:
            print(f"✓ {service_name} is healthy")
            return True
        else:
            print(f"✗ {service_name} health check failed: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"✗ {service_name} health check failed: {e}")
        return False

def login_as_admin(base_url):
    """Login and get JWT token"""
    login_data = {
        "username": "admin",
        "password": "password"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            response_data = response.json()
            token = response_data.get("data", {}).get("token")
            if token:
                print("✓ Successfully logged in as admin")
                return token
            else:
                print(f"No token in login response: {response_data}")
                return None
        else:
            print(f"✗ Login failed: {response.status_code}")
            return None
    except requests.RequestException as e:
        print(f"✗ Login failed: {e}")
        return None

def generate_large_data(size_mb):
    """Generate large string data of specified size in MB"""
    size_bytes = size_mb * 1024 * 1024
    # Generate random string data
    chars = string.ascii_letters + string.digits + " " * 10  # Add spaces for realism
    return ''.join(random.choice(chars) for _ in range(size_bytes))

def create_user_with_large_data(base_url, token, username, data_size_mb):
    """Create a user with large data that will force value log files"""
    large_data = generate_large_data(data_size_mb)
    
    user_data = {
        "username": username,
        "password": "password123",
        "is_admin": False,
        # Store large data in a custom field (this may not work with the actual API)
        # But we'll try to create large JSON payload
        "large_profile_data": large_data,
        "description": large_data[:1000],  # Truncated for description
        "notes": large_data[1000:2000] if len(large_data) > 1000 else large_data
    }
    
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    try:
        response = requests.post(f"{base_url}/api/v1/users", json=user_data, headers=headers, timeout=30)
        if response.status_code == 200:
            response_data = response.json()
            user_data = response_data.get("data", {}).get("user", {})
            user_id = user_data.get("uuid")
            if user_id:
                return user_id
        return None
    except requests.RequestException:
        return None

def update_user_with_large_data(base_url, token, user_id, data_size_mb):
    """Update user with different large data to create fragmentation"""
    large_data = generate_large_data(data_size_mb)
    
    update_data = {
        "is_admin": False,
        "large_profile_data": large_data,
        "description": large_data[:1000],
        "notes": large_data[1000:2000] if len(large_data) > 1000 else large_data
    }
    
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    try:
        response = requests.patch(f"{base_url}/api/v1/users/{user_id}", json=update_data, headers=headers, timeout=30)
        return response.status_code == 200
    except requests.RequestException:
        return False

def delete_user(base_url, token, user_id):
    """Delete a user"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.delete(f"{base_url}/api/v1/users/{user_id}", headers=headers, timeout=10)
        return response.status_code == 200
    except requests.RequestException:
        return False

def main():
    print("🧪 BADGERDB VALUE LOG GC TEST")
    print("=" * 50)
    
    # Clean up first
    cleanup_databases()
    
    try:
        # Start management service
        management_process = run_service("management", "test_vlog_gc.yml", "Management")
        time.sleep(3)
        
        # Check health
        if not check_service_health("http://localhost:8003", "Management"):
            print("❌ Management service not healthy, exiting")
            return
        
        # Login as admin
        token = login_as_admin("http://localhost:8003")
        if not token:
            print("❌ Could not login, exiting")
            return
        
        print(f"✓ Token received")
        
        # Phase 1: Create users with LARGE data (5MB each) to force value log files
        print(f"\n📊 PHASE 1: Creating users with LARGE data (5MB each)")
        created_users = []
        for i in range(10):  # Only 10 users but with 5MB each = 50MB total
            print(f"Creating user {i+1}/10 with 5MB data...")
            user_id = create_user_with_large_data("http://localhost:8003", token, f"biguser{i}", 5)
            if user_id:
                created_users.append(user_id)
                print(f"✓ Created user {user_id}")
            else:
                print(f"✗ Failed to create user {i}")
            
            time.sleep(0.5)  # Brief pause between creates
        
        print(f"✅ Created {len(created_users)} users with large data")
        time.sleep(5)  # Wait for GC to potentially run
        
        # Phase 2: Update users with different large data (fragmentation)
        print(f"\n🔄 PHASE 2: Updating users with different large data (creating fragmentation)")
        for i, user_id in enumerate(created_users):
            print(f"Updating user {i+1}/{len(created_users)} with new 5MB data...")
            success = update_user_with_large_data("http://localhost:8003", token, user_id, 5)
            if success:
                print(f"✓ Updated user {user_id}")
            else:
                print(f"✗ Failed to update user {user_id}")
            
            time.sleep(0.5)
        
        print(f"✅ Updated {len(created_users)} users")
        time.sleep(5)  # Wait for GC
        
        # Phase 3: Update again (more fragmentation)
        print(f"\n🔄 PHASE 3: Second update round (more fragmentation)")
        for i, user_id in enumerate(created_users):
            print(f"Second update for user {i+1}/{len(created_users)}...")
            success = update_user_with_large_data("http://localhost:8003", token, user_id, 5)
            if success:
                print(f"✓ Re-updated user {user_id}")
            
            time.sleep(0.5)
        
        print(f"✅ Re-updated {len(created_users)} users")
        time.sleep(5)
        
        # Phase 4: Delete half of users (create stale data)
        print(f"\n🗑️ PHASE 4: Deleting half of users (creating stale data)")
        deleted_count = 0
        for i, user_id in enumerate(created_users[:len(created_users)//2]):
            print(f"Deleting user {i+1}...")
            success = delete_user("http://localhost:8003", token, user_id)
            if success:
                deleted_count += 1
                print(f"✓ Deleted user {user_id}")
            else:
                print(f"✗ Failed to delete user {user_id}")
            
            time.sleep(0.5)
        
        print(f"✅ Deleted {deleted_count} users")
        
        # Phase 5: Wait and monitor GC
        print(f"\n⏱️ PHASE 5: Monitoring GC for 30 seconds...")
        print("💡 Check the management service logs for GC activity!")
        print("💡 Look for 'Value log GC' or 'discard' messages")
        
        for i in range(30):
            print(f"Monitoring... {i+1}/30 seconds", end='\r')
            time.sleep(1)
        
        print(f"\n✅ VLOG GC TEST COMPLETED!")
        print(f"📈 SUMMARY:")
        print(f"   • Created: {len(created_users)} users with 5MB each = {len(created_users) * 5}MB total")
        print(f"   • Updated: {len(created_users)} users twice = {len(created_users) * 10}MB additional")
        print(f"   • Deleted: {deleted_count} users = {deleted_count * 15}MB stale data")
        print(f"   • Total data operations: ~{(len(created_users) * 15) + (deleted_count * 15)}MB")
        print(f"   • Expected stale data: ~{deleted_count * 15}MB")
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        try:
            management_process.terminate()
            management_process.wait(timeout=5)
        except:
            management_process.kill()
        
        cleanup_databases()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    main() 